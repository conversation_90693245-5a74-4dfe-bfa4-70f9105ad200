<template>
  <div class="sweep-test-page fixed inset-0 z-50 bg-gray-50 flex flex-col">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Sweep Operations Test Suite</h1>
        <p class="text-gray-600 mt-1">Comprehensive testing for OpenCascade.js sweep operations and boolean geometry</p>
      </div>
      <button
        @click="closeTestPage"
        class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded transition-colors"
      >
        Close
      </button>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex overflow-hidden">
      <!-- Left Panel - Test Controls -->
      <div class="w-1/3 bg-white border-r border-gray-200 flex flex-col">
        <div class="p-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900">Test Categories</h2>
        </div>
        
        <div class="flex-1 overflow-y-auto p-4 space-y-4">
          <!-- Basic Tests -->
          <div class="test-category">
            <h3 class="font-medium text-gray-900 mb-2">Basic Sweep Tests</h3>
            <div class="space-y-2">
              <button 
                @click="runTest('basic-door-body')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('basic-door-body')"
              >
                <div class="font-medium">Door Body Creation</div>
                <div class="text-sm text-gray-600">Test basic door panel generation from PANEL layer</div>
              </button>
              
              <button 
                @click="runTest('basic-tool-brep')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('basic-tool-brep')"
              >
                <div class="font-medium">Tool BRep Generation</div>
                <div class="text-sm text-gray-600">Create 3D tool geometries for all tool types</div>
              </button>
              
              <button 
                @click="runTest('basic-sweep')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('basic-sweep')"
              >
                <div class="font-medium">Simple Sweep Operation</div>
                <div class="text-sm text-gray-600">Basic material removal with single tool</div>
              </button>
            </div>
          </div>

          <!-- Shape Tests -->
          <div class="test-category">
            <h3 class="font-medium text-gray-900 mb-2">Shape Tests with Cylindrical Tools</h3>
            <div class="space-y-2">
              <button
                @click="runTest('rectangle-operations')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('rectangle-operations')"
              >
                <div class="font-medium">Rectangle Operations</div>
                <div class="text-sm text-gray-600">Rectangular pockets and profiles with cylindrical tools</div>
              </button>

              <button
                @click="runTest('line-operations')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('line-operations')"
              >
                <div class="font-medium">Line Operations</div>
                <div class="text-sm text-gray-600">Linear cuts and grooves with cylindrical tools</div>
              </button>

              <button
                @click="runTest('polyline-operations')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('polyline-operations')"
              >
                <div class="font-medium">Polyline Operations</div>
                <div class="text-sm text-gray-600">Complex path following with cylindrical tools</div>
              </button>

              <button
                @click="runTest('polyline-performance')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('polyline-performance')"
              >
                <div class="font-medium">🚀 Polyline Performance</div>
                <div class="text-sm text-gray-600">Wire-based sweep optimization test</div>
              </button>

              <button
                @click="runTest('polyline-sweep')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('polyline-sweep')"
              >
                <div class="font-medium">✨ Polyline Sweep</div>
                <div class="text-sm text-gray-600">Test new createSweepFromPolyline function</div>
              </button>

              <button
                @click="runTest('arc-operations')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('arc-operations')"
              >
                <div class="font-medium">Arc Operations</div>
                <div class="text-sm text-gray-600">Curved cuts and profiles with cylindrical tools</div>
              </button>
            </div>
          </div>

          <!-- Depth Testing -->
          <div class="test-category">
            <h3 class="font-medium text-gray-900 mb-2">Depth & Thickness Tests</h3>
            <div class="space-y-2">
              <button
                @click="runTest('shallow-depth-test')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('shallow-depth-test')"
              >
                <div class="font-medium">🔍 Shallow Depth Test</div>
                <div class="text-sm text-gray-600">Test 1-3mm depths on 18mm door</div>
              </button>

              <button
                @click="runTest('medium-depth-test')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('medium-depth-test')"
              >
                <div class="font-medium">🔍 Medium Depth Test</div>
                <div class="text-sm text-gray-600">Test 5-10mm depths on 18mm door</div>
              </button>

              <button
                @click="runTest('deep-depth-test')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('deep-depth-test')"
              >
                <div class="font-medium">🔍 Deep Depth Test</div>
                <div class="text-sm text-gray-600">Test 12-15mm depths on 18mm door</div>
              </button>

              <button
                @click="runTest('through-hole-test')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('through-hole-test')"
              >
                <div class="font-medium">🔍 Through-Hole Test</div>
                <div class="text-sm text-gray-600">Test 18mm+ depths (through door)</div>
              </button>

              <button
                @click="runTest('variable-thickness-test')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('variable-thickness-test')"
              >
                <div class="font-medium">🔍 Variable Thickness Test</div>
                <div class="text-sm text-gray-600">Multiple door thicknesses (12mm, 18mm, 25mm)</div>
              </button>
            </div>
          </div>

          <!-- Advanced Tests -->
          <div class="test-category">
            <h3 class="font-medium text-gray-900 mb-2">Advanced Sweep Tests</h3>
            <div class="space-y-2">
              <button
                @click="runTest('multi-tool-sweep')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('multi-tool-sweep')"
              >
                <div class="font-medium">Multi-Tool Operations</div>
                <div class="text-sm text-gray-600">Multiple tools with different operations</div>
              </button>

              <button
                @click="runTest('complex-geometry')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('complex-geometry')"
              >
                <div class="font-medium">Complex Geometry</div>
                <div class="text-sm text-gray-600">Advanced shapes with curves and fillets</div>
              </button>

              <button
                @click="runTest('boolean-operations')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('boolean-operations')"
              >
                <div class="font-medium">Boolean Operations</div>
                <div class="text-sm text-gray-600">Union, subtract, intersect operations</div>
              </button>
            </div>
          </div>

          <!-- Performance Tests -->
          <div class="test-category">
            <h3 class="font-medium text-gray-900 mb-2">Performance Tests</h3>
            <div class="space-y-2">
              <button 
                @click="runTest('large-model')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('large-model')"
              >
                <div class="font-medium">Large Model Test</div>
                <div class="text-sm text-gray-600">Test with complex door model and many tools</div>
              </button>
              
              <button 
                @click="runTest('stress-test')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('stress-test')"
              >
                <div class="font-medium">Stress Test</div>
                <div class="text-sm text-gray-600">Maximum geometry complexity test</div>
              </button>
            </div>
          </div>

          <!-- Utility Actions -->
          <div class="test-category border-t pt-4">
            <h3 class="font-medium text-gray-900 mb-2">Utilities</h3>
            <div class="space-y-2">
              <button 
                @click="clearResults"
                class="w-full p-2 text-sm bg-gray-100 hover:bg-gray-200 rounded border"
              >
                Clear All Results
              </button>
              
              <button 
                @click="runAllTests"
                :disabled="isRunning"
                class="w-full p-2 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded disabled:opacity-50"
              >
                Run All Tests
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel - Results and Visualization -->
      <div class="flex-1 flex flex-col">
        <!-- Results Header -->
        <div class="bg-white border-b border-gray-200 p-4">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900">Test Results</h2>
            <div class="flex items-center space-x-2">
              <span v-if="isRunning" class="text-sm text-blue-600">
                <span class="inline-block animate-spin mr-1">⚙️</span>
                {{ currentTest }}
              </span>
              <span v-else-if="lastTestResult" class="text-sm" :class="lastTestResult.success ? 'text-green-600' : 'text-red-600'">
                {{ lastTestResult.success ? '✅ Success' : '❌ Failed' }}
              </span>
            </div>
          </div>
        </div>

        <!-- Results Content -->
        <div class="flex-1 flex">
          <!-- Console Output -->
          <div class="w-1/2 border-r border-gray-200 flex flex-col">
            <div class="bg-gray-100 px-4 py-2 border-b border-gray-200">
              <h3 class="font-medium text-gray-900">Console Output</h3>
            </div>
            <div class="flex-1 overflow-y-auto p-4 bg-gray-900 text-green-400 font-mono text-sm">
              <div v-for="(log, index) in consoleOutput" :key="index" class="mb-1">
                <span class="text-gray-500">[{{ log.timestamp }}]</span>
                <span :class="getLogClass(log.level)">{{ log.message }}</span>
              </div>
              <div v-if="consoleOutput.length === 0" class="text-gray-500">
                No output yet. Run a test to see results.
              </div>
            </div>
          </div>

          <!-- 3D Visualization -->
          <div class="w-1/2 flex flex-col">
            <div class="bg-gray-100 px-4 py-2 border-b border-gray-200 flex items-center justify-between">
              <h3 class="font-medium text-gray-900">3D Visualization</h3>
              <div class="flex items-center space-x-2">
                <label class="flex items-center space-x-1 text-sm">
                  <input
                    type="checkbox"
                    v-model="wireframeMode"
                    @change="toggleWireframe"
                    class="rounded"
                  >
                  <span>Wireframe</span>
                </label>
                <label class="flex items-center space-x-1 text-sm">
                  <input
                    type="checkbox"
                    v-model="debugMode"
                    @change="toggleDebugMarkers"
                    class="rounded"
                  >
                  <span>Debug Markers</span>
                </label>
                <button
                  @click="resetCamera"
                  class="px-2 py-1 text-xs bg-gray-200 hover:bg-gray-300 rounded"
                >
                  Reset View
                </button>
              </div>
            </div>
            <div class="flex-1 relative">
              <div 
                ref="threeContainer" 
                class="w-full h-full bg-gray-800"
                :class="{ 'opacity-50': isRunning }"
              ></div>
              
              <!-- Loading Overlay -->
              <div v-if="isRunning" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                <div class="text-white text-center">
                  <div class="animate-spin text-4xl mb-2">⚙️</div>
                  <div>{{ processingStep || 'Processing...' }}</div>
                </div>
              </div>
              
              <!-- Empty State -->
              <div v-if="!modelUrl && !isRunning" class="absolute inset-0 flex items-center justify-center text-gray-500">
                <div class="text-center">
                  <div class="text-6xl mb-4">🔧</div>
                  <div>Run a test to see 3D results</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { OrbitControls } from '../utils/OrbitControls'

// Define emits
const emit = defineEmits<{
  close: []
}>()

// Function to close the test page
const closeTestPage = () => {
  emit('close')
}

// Test state
const isRunning = ref(false)
const currentTest = ref('')
const lastTestResult = ref<{ success: boolean; message: string } | null>(null)
const testResults = ref<Map<string, { success: boolean; message: string; timestamp: string }>>(new Map())

// Console output
interface LogEntry {
  timestamp: string
  level: 'info' | 'success' | 'error' | 'warning'
  message: string
}
const consoleOutput = ref<LogEntry[]>([])

// 3D Visualization
const threeContainer = ref<HTMLElement | null>(null)
const modelUrl = ref<string | null>(null)
const processingStep = ref<string>('')
const wireframeMode = ref<boolean>(false)
const debugMode = ref<boolean>(false)

// Three.js objects
let scene: THREE.Scene | null = null
let camera: THREE.PerspectiveCamera | null = null
let renderer: THREE.WebGLRenderer | null = null
let controls: OrbitControls | null = null
let model: THREE.Object3D | null = null
let debugMarkers: THREE.Object3D[] = []

// OCJS Worker
let worker: Worker | null = null
const pendingMessages = new Map<string, { resolve: Function; reject: Function }>()

// Test definitions
const testDefinitions = {
  'basic-door-body': {
    name: 'Door Body Creation',
    description: 'Creates a basic door panel from PANEL layer geometry',
    luaScript: `
-- Basic Door Body Test
X = 200
Y = 150
materialThickness = 18

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()
    print("✅ Door body test geometry created")
    return true
end

require "ADekoDebugMode"
    `
  },
  'basic-tool-brep': {
    name: 'Tool BRep Generation',
    description: 'Tests creation of 3D tool geometries for different tool types',
    luaScript: `
-- Tool BRep Test
X = 100
Y = 100
materialThickness = 18

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()

    G.setLayer("8MM")
    G.setThickness(-3)
    G.circle({50, 50}, 4)

    print("✅ Tool BRep test geometry created")
    return true
end

require "ADekoDebugMode"
    `
  },
  'basic-sweep': {
    name: 'Simple Sweep Operation',
    description: 'Basic material removal with a single cylindrical tool',
    luaScript: `
-- Basic Sweep Test
X = 150
Y = 100
materialThickness = 18

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()

    G.setLayer("6MM")
    G.setThickness(-3)
    G.rectangle({25, 25}, {125, 75})

    print("✅ Basic sweep test geometry created")
    return true
end

require "ADekoDebugMode"
    `
  },
  'rectangle-operations': {
    name: 'Rectangle Operations',
    description: 'Tests rectangular pockets and profiles with cylindrical tools',
    luaScript: `
-- Rectangle Operations Test
X = 250
Y = 200
materialThickness = 20

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()

    -- Large rectangular pocket with 12MM tool
    G.setLayer("12MM")
    G.setThickness(-8)
    G.rectangle({50, 50}, {200, 100})

    -- Medium rectangular groove with 8MM tool
    G.setLayer("8MM")
    G.setThickness(-5)
    G.rectangle({30, 120}, {120, 170})

    -- Small rectangular profile with 6MM tool
    G.setLayer("6MM")
    G.setThickness(-3)
    G.rectangle({150, 120}, {220, 170})

    print("✅ Rectangle operations test geometry created")
    return true
end

require "ADekoDebugMode"
    `
  },
  'line-operations': {
    name: 'Line Operations',
    description: 'Tests linear cuts and grooves with cylindrical tools',
    luaScript: `
-- Line Operations Test
X = 200
Y = 150
materialThickness = 18

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()

    -- Horizontal line cut with 10MM tool
    G.setLayer("10MM")
    G.setThickness(-6)
    G.line({20, 40}, {180, 40})

    -- Vertical line cut with 8MM tool
    G.setLayer("8MM")
    G.setThickness(-4)
    G.line({100, 20}, {100, 130})

    -- Diagonal line cut with 6MM tool
    G.setLayer("6MM")
    G.setThickness(-3)
    G.line({30, 80}, {170, 120})

    print("✅ Line operations test geometry created")
    return true
end

require "ADekoDebugMode"
    `
  },
  'polyline-operations': {
    name: 'Polyline Operations',
    description: 'Tests complex path following with cylindrical tools',
    luaScript: `
-- Polyline Operations Test
X = 220
Y = 180
materialThickness = 20

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()

    -- Complex decorative border with 8MM tool
    G.setLayer("K_8MM_GROOVE")
    G.setThickness(-4)
    G.polyline({20, 20}, {200, 20}, {200, 160}, {20, 160}, {20, 20})

    -- Zigzag pattern with 6MM tool
    G.setLayer("K_6MM_GROOVE")
    G.setThickness(-3)
    G.polyline({40, 50}, {80, 80}, {120, 50}, {160, 80}, {180, 50})

    -- Star pattern with 4MM tool
    G.setLayer("K_4MM_GROOVE")
    G.setThickness(-2)
    G.polyline({110, 120}, {130, 100}, {150, 120}, {130, 140}, {110, 120})

    print("✅ Polyline operations test geometry created")
    return true
end

require "ADekoDebugMode"
    `
  },
  'polyline-performance': {
    name: 'Polyline Performance Test',
    description: 'Tests wire-based vs cylinder-based polyline sweeping performance',
    luaScript: `
-- Polyline Performance Test - Wire-based Sweep Optimization
X = 300
Y = 250
materialThickness = 20

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()

    -- Complex spiral groove with many segments to test performance
    G.setLayer("K_6MM_GROOVE")  -- K_ prefix = groove operation
    G.setThickness(-3)
    G.polyline(
        {50, 50},   {70, 50},   {70, 70},   {50, 70},   {50, 90},   {90, 90},
        {90, 50},   {110, 50},  {110, 110}, {50, 110},  {50, 130},  {130, 130},
        {130, 50},  {150, 50},  {150, 150}, {50, 150},  {50, 170},  {170, 170},
        {170, 50},  {190, 50},  {190, 190}, {50, 190},  {50, 210},  {210, 210},
        {210, 50},  {230, 50},  {230, 230}, {50, 230}
    )

    print("✅ Complex spiral polyline created for performance testing")
    print("   - 27 segments in spiral pattern")
    print("   - Wire-based sweep should be significantly faster")
    print("   - Check console for timing information")
    return true
end

require "ADekoDebugMode"
    `
  },
  'arc-operations': {
    name: 'Arc Operations',
    description: 'Tests curved cuts and profiles with cylindrical tools',
    luaScript: `
-- Arc Operations Test
X = 200
Y = 150
materialThickness = 18

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()

    -- Large semicircle arc with 10MM tool
    G.setLayer("10MM")
    G.setThickness(-5)
    G.arc({100, 75}, 40, 0, 180)

    -- Quarter circle arc with 8MM tool
    G.setLayer("8MM")
    G.setThickness(-4)
    G.arc({50, 50}, 25, 0, 90)

    -- Three-quarter circle arc with 6MM tool
    G.setLayer("6MM")
    G.setThickness(-3)
    G.arc({150, 100}, 20, 45, 315)

    print("✅ Arc operations test geometry created")
    return true
end

require "ADekoDebugMode"
    `
  },
  'polyline-sweep': {
    name: 'Polyline Sweep Test',
    description: 'Tests the new createSweepFromPolyline function with custom polyline paths',
    luaScript: `
-- Polyline Sweep Test - Direct createSweepFromPolyline function test
X = 200
Y = 150
materialThickness = 18

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()

    -- Create a custom polyline path for sweep testing
    -- This will be processed by the createSweepFromPolyline function
    print("✅ Polyline sweep test geometry created")
    return true
end

require "ADekoDebugMode"
    `
  },
  'multi-tool-sweep': {
    name: 'Multi-Tool Operations',
    description: 'Tests drilling, groove, and pocket operations based on layer names and radius values',
    luaScript: `
-- Multi-Tool Sweep Test
X = 200
Y = 150
materialThickness = 20

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()

    G.setLayer("8MM")
    G.setThickness(-5)
    G.rectangle({20, 20}, {100, 60})

    G.setLayer("6MM")
    G.setThickness(-3)
    G.circle({50, 100}, 10)

    G.setLayer("4MM")
    G.setThickness(-2)
    G.line({120, 30}, {180, 30})

    print("✅ Multi-tool test geometry created")
    return true
end

require "ADekoDebugMode"
    `
  },
  'complex-geometry': {
    name: 'Complex Geometry',
    description: 'Advanced shapes with curves, fillets, and complex profiles',
    luaScript: `
-- Complex Geometry Test
G.start()
G.makePart(250, 200)
G.setLayer("PANEL")
G.rectangle({0, 0}, {250, 200})
G.setLayer("V120")
G.polyline({10,10}, {240,10}, {240,190}, {10,190}, {10,10})
G.setLayer("12MM")
G.circle({125, 100}, 30)
G.setLayer("BALLNOSE6")
G.rectangle({50, 50}, {200, 150})
G.finish()
print("✅ Complex geometry test created")
    `
  },
  'boolean-operations': {
    name: 'Boolean Operations',
    description: 'Tests union, subtract, and intersect operations',
    luaScript: `
-- Boolean Operations Test
G.start()
G.makePart(180, 120)
G.setLayer("PANEL")
G.rectangle({0, 0}, {180, 120})
G.setLayer("10MM")
G.circle({45, 60}, 20)
G.setLayer("8MM")
G.rectangle({90, 30}, {150, 90})
G.setLayer("6MM")
G.circle({135, 60}, 15)
G.finish()
print("✅ Boolean operations test geometry created")
    `
  },
  'large-model': {
    name: 'Large Model Test',
    description: 'Performance test with complex door model and many tools',
    luaScript: `
-- Large Model Performance Test
G.start()
G.makePart(400, 300)
G.setLayer("PANEL")
G.rectangle({0, 0}, {400, 300})
-- Create multiple tool operations
for i = 1, 10 do
  G.setLayer("8MM")
  G.circle({50 + i * 30, 50}, 8)
  G.setLayer("6MM")
  G.rectangle({20 + i * 35, 150}, {45 + i * 35, 250})
end
G.finish()
print("✅ Large model test geometry created")
    `
  },
  'stress-test': {
    name: 'Stress Test',
    description: 'Maximum complexity test with numerous operations',
    luaScript: `
-- Stress Test - Maximum Complexity
G.start()
G.makePart(500, 400)
G.setLayer("PANEL")
G.rectangle({0, 0}, {500, 400})
-- Create many different tool types and operations
local tools = {"20MM", "16MM", "12MM", "10MM", "8MM", "6MM", "4MM", "3MM", "V120", "V90", "BALLNOSE8", "BALLNOSE6"}
for i, tool in ipairs(tools) do
  G.setLayer(tool)
  local x = (i % 5) * 90 + 50
  local y = math.floor((i-1) / 5) * 80 + 50
  if tool:find("V") then
    G.polyline({x-20,y-20}, {x+20,y-20}, {x+20,y+20}, {x-20,y+20}, {x-20,y-20})
  elseif tool:find("BALLNOSE") then
    G.circle({x, y}, 15)
  else
    G.rectangle({x-15, y-15}, {x+15, y+15})
  end
end
G.finish()
print("✅ Stress test geometry created")
    `
  },
  'shallow-depth-test': {
    name: 'Shallow Depth Test',
    description: 'Tests very shallow cuts (1-3mm) to verify depth visualization',
    luaScript: `
-- Shallow Depth Test - 1-3mm depths on 18mm door
X = 200
Y = 150
materialThickness = 18

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()

    -- 1mm depth circle with 8MM tool
    G.setLayer("8MM_1MM_DEPTH")
    G.setThickness(-1)
    G.circle({50, 50}, 15)

    -- 2mm depth rectangle with 6MM tool
    G.setLayer("6MM_2MM_DEPTH")
    G.setThickness(-2)
    G.rectangle({100, 30}, {150, 70})

    -- 3mm depth line with 4MM tool
    G.setLayer("4MM_3MM_DEPTH")
    G.setThickness(-3)
    G.line({30, 100}, {170, 100})

    print("✅ Shallow depth test geometry created")
    print("   - 1mm depth: Circle at (50,50)")
    print("   - 2mm depth: Rectangle at (100,30)-(150,70)")
    print("   - 3mm depth: Line from (30,100) to (170,100)")
    return true
end

require "ADekoDebugMode"
    `
  },
  'medium-depth-test': {
    name: 'Medium Depth Test',
    description: 'Tests medium cuts (5-10mm) to verify depth visualization',
    luaScript: `
-- Medium Depth Test - 5-10mm depths on 18mm door
X = 200
Y = 150
materialThickness = 18

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()

    -- 5mm depth circle with 10MM tool
    G.setLayer("10MM_5MM_DEPTH")
    G.setThickness(-5)
    G.circle({50, 50}, 20)

    -- 7mm depth rectangle with 8MM tool
    G.setLayer("8MM_7MM_DEPTH")
    G.setThickness(-7)
    G.rectangle({100, 30}, {170, 80})

    -- 10mm depth polyline with 6MM tool
    G.setLayer("6MM_10MM_DEPTH")
    G.setThickness(-10)
    G.polyline({30, 100}, {80, 100}, {80, 130}, {30, 130}, {30, 100})

    print("✅ Medium depth test geometry created")
    print("   - 5mm depth: Circle at (50,50)")
    print("   - 7mm depth: Rectangle at (100,30)-(170,80)")
    print("   - 10mm depth: Polyline rectangle at (30,100)")
    return true
end

require "ADekoDebugMode"
    `
  },
  'deep-depth-test': {
    name: 'Deep Depth Test',
    description: 'Tests deep cuts (12-15mm) to verify depth visualization',
    luaScript: `
-- Deep Depth Test - 12-15mm depths on 18mm door
X = 200
Y = 150
materialThickness = 18

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()

    -- 12mm depth circle with 12MM tool
    G.setLayer("12MM_12MM_DEPTH")
    G.setThickness(-12)
    G.circle({50, 50}, 25)

    -- 15mm depth rectangle with 10MM tool
    G.setLayer("10MM_15MM_DEPTH")
    G.setThickness(-15)
    G.rectangle({100, 30}, {170, 80})

    -- 14mm depth arc with 8MM tool
    G.setLayer("8MM_14MM_DEPTH")
    G.setThickness(-14)
    G.arc({130, 110}, 30, 0, 180)

    print("✅ Deep depth test geometry created")
    print("   - 12mm depth: Circle at (50,50)")
    print("   - 15mm depth: Rectangle at (100,30)-(170,80)")
    print("   - 14mm depth: Arc at (130,110)")
    return true
end

require "ADekoDebugMode"
    `
  },
  'through-hole-test': {
    name: 'Through-Hole Test',
    description: 'Tests through-holes (18mm+ depths) to verify complete penetration',
    luaScript: `
-- Through-Hole Test - 18mm+ depths on 18mm door
X = 200
Y = 150
materialThickness = 18

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()

    -- 18mm depth (exactly through) with 8MM tool
    G.setLayer("8MM_18MM_THROUGH")
    G.setThickness(-18)
    G.circle({50, 50}, 10)

    -- 20mm depth (over-penetration) with 6MM tool
    G.setLayer("6MM_20MM_OVER")
    G.setThickness(-20)
    G.circle({100, 50}, 8)

    -- 25mm depth (deep over-penetration) with 4MM tool
    G.setLayer("4MM_25MM_DEEP")
    G.setThickness(-25)
    G.circle({150, 50}, 6)

    -- Through rectangle with 10MM tool
    G.setLayer("10MM_THROUGH_RECT")
    G.setThickness(-22)
    G.rectangle({60, 90}, {140, 120})

    print("✅ Through-hole test geometry created")
    print("   - 18mm depth: Exact through-hole at (50,50)")
    print("   - 20mm depth: Over-penetration at (100,50)")
    print("   - 25mm depth: Deep over-penetration at (150,50)")
    print("   - 22mm depth: Through rectangle at (60,90)-(140,120)")
    return true
end

require "ADekoDebugMode"
    `
  },
  'variable-thickness-test': {
    name: 'Variable Thickness Test',
    description: 'Tests different door thicknesses with same depth cuts',
    luaScript: `
-- Variable Thickness Test - Multiple door thicknesses
X = 300
Y = 200
materialThickness = 18  -- This will be overridden in the test

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()

    -- 5mm depth cut (will be tested on 12mm, 18mm, and 25mm doors)
    G.setLayer("8MM_5MM_DEPTH")
    G.setThickness(-5)
    G.circle({150, 100}, 20)

    print("✅ Variable thickness test geometry created")
    print("   - 5mm depth cut on variable thickness door")
    return true
end

require "ADekoDebugMode"
    `
  }
}

onMounted(() => {
  initializeWorker()
  initThreeJS()
})

onUnmounted(() => {
  cleanup()
})

function initializeWorker() {
  try {
    worker = new Worker('/src/workers/ocjsWorker.ts', { type: 'module' })
    
    worker.onmessage = (event) => {
      const { id, type, data, error } = event.data
      const pending = pendingMessages.get(id)
      
      if (pending) {
        pendingMessages.delete(id)
        if (type === 'success') {
          pending.resolve(data)
        } else {
          pending.reject(new Error(error || 'Unknown worker error'))
        }
      }
    }

    worker.onerror = (error) => {
      addLog('error', `Worker error: ${error.message}`)
    }
    
    addLog('success', 'OCJS Worker initialized successfully')
  } catch (error) {
    addLog('error', `Failed to initialize worker: ${error}`)
  }
}

function initThreeJS(): boolean {
  if (!threeContainer.value) return false

  try {
    // Scene
    scene = new THREE.Scene()
    scene.background = new THREE.Color(0xffffff) // White background

    // Camera
    camera = new THREE.PerspectiveCamera(75, threeContainer.value.clientWidth / threeContainer.value.clientHeight, 0.1, 1000)
    camera.position.set(150, 150, 150)

    // Renderer
    renderer = new THREE.WebGLRenderer({ antialias: true })
    renderer.setSize(threeContainer.value.clientWidth, threeContainer.value.clientHeight)
    renderer.shadowMap.enabled = false // Disabled due to Three.js proxy issues
    renderer.setClearColor(0xffffff) // White background
    threeContainer.value.appendChild(renderer.domElement)

    // Controls
    controls = new OrbitControls(camera, renderer.domElement)
    // Note: enableDamping and dampingFactor are private in this version

    // Lighting setup for better model visibility
    const ambientLight = new THREE.AmbientLight(0x404040, 0.8)
    scene.add(ambientLight)

    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0)
    directionalLight1.position.set(100, 100, 50)
    scene.add(directionalLight1)

    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.5)
    directionalLight2.position.set(-100, 50, -50)
    scene.add(directionalLight2)

    // Add coordinate axes for reference
    const axesHelper = new THREE.AxesHelper(50)
    scene.add(axesHelper)

    // Start render loop
    animate()
    
    addLog('success', 'Three.js initialized successfully')
    return true
  } catch (error) {
    addLog('error', `Failed to initialize Three.js: ${error}`)
    return false
  }
}

function animate() {
  if (!renderer || !scene || !camera || !controls) return
  
  requestAnimationFrame(animate)
  controls.update()
  renderer.render(scene, camera)
}

async function sendMessage(type: string, data: any): Promise<any> {
  if (!worker) throw new Error('Worker not initialized')
  
  const id = Math.random().toString(36).substring(2, 11)
  
  return new Promise((resolve, reject) => {
    pendingMessages.set(id, { resolve, reject })
    worker!.postMessage({ id, type, data })
  })
}

function addLog(level: LogEntry['level'], message: string) {
  const timestamp = new Date().toLocaleTimeString()
  consoleOutput.value.push({ timestamp, level, message })
  
  // Auto-scroll to bottom
  setTimeout(() => {
    const consoleEl = document.querySelector('.overflow-y-auto')
    if (consoleEl) {
      consoleEl.scrollTop = consoleEl.scrollHeight
    }
  }, 10)
}

function getLogClass(level: string): string {
  switch (level) {
    case 'success': return 'text-green-400'
    case 'error': return 'text-red-400'
    case 'warning': return 'text-yellow-400'
    default: return 'text-green-400'
  }
}

function getTestButtonClass(testId: string): string {
  const result = testResults.value.get(testId)
  if (!result) return 'border-gray-300'
  return result.success ? 'border-green-300 bg-green-50' : 'border-red-300 bg-red-50'
}

async function runTest(testId: string) {
  if (isRunning.value) return
  
  const testDef = testDefinitions[testId as keyof typeof testDefinitions]
  if (!testDef) {
    addLog('error', `Unknown test: ${testId}`)
    return
  }

  isRunning.value = true
  currentTest.value = testDef.name
  
  addLog('info', `🧪 Starting test: ${testDef.name}`)
  addLog('info', testDef.description)
  
  try {
    // Execute Lua script (simulated for now)
    processingStep.value = 'Executing Lua script...'
    addLog('info', `📝 Executing Lua script for ${testDef.name}`)
    await new Promise(resolve => setTimeout(resolve, 500))

    // Process with OCJS worker based on test type
    processingStep.value = 'Processing with OCJS worker...'

    let finalShapeId: string

    if (testId === 'basic-door-body') {
      // Just create door body (no operations)
      const doorResult = await sendMessage('createDoorBody', {
        width: 200,
        height: 150,
        thickness: 18
      })
      addLog('success', `✅ Door body created: ${doorResult.shapeId}`)
      addLog('info', '📋 This test shows just the door body without any cuts')
      finalShapeId = doorResult.shapeId

    } else if (testId === 'basic-tool-brep') {
      // Create door body + simple tool BRep (at center for now)
      const doorResult = await sendMessage('createDoorBody', {
        width: 100,
        height: 100,
        thickness: 18
      })
      addLog('success', `✅ Door body created: ${doorResult.shapeId}`)

      // Create simple tool BRep (this should work)
      processingStep.value = 'Creating tool BRep...'
      const toolResult = await sendMessage('createToolBRep', {
        tool: { name: '8MM', type: 'cylindrical', diameter: 8 },
        height: 25 // Make it taller than door thickness
      })
      addLog('success', `✅ Tool BRep created: ${toolResult.shapeId}`)

      // Perform sweep operation using the tool shape
      processingStep.value = 'Performing sweep operation...'
      const sweepResult = await sendMessage('performSweepOperation', {
        doorBodyShape: doorResult.shapeId,
        toolGeometries: [toolResult.shapeId],
        operation: 'subtract'
      })
      addLog('success', `✅ Sweep operation completed: ${sweepResult.toolsProcessed} tools processed`)
      finalShapeId = sweepResult.shapeId

    } else if (testId === 'basic-sweep') {
      // Create door body + simple tool cut
      const doorResult = await sendMessage('createDoorBody', {
        width: 150,
        height: 100,
        thickness: 18
      })
      addLog('success', `✅ Door body created: ${doorResult.shapeId}`)

      // Create simple tool BRep
      processingStep.value = 'Creating tool BRep...'
      const toolResult = await sendMessage('createToolBRep', {
        tool: { name: '6MM', type: 'cylindrical', diameter: 6 },
        height: 25 // Through-hole depth
      })
      addLog('success', `✅ Tool BRep created: ${toolResult.shapeId}`)

      // Perform sweep operation
      processingStep.value = 'Performing sweep operation...'
      const sweepResult = await sendMessage('performSweepOperation', {
        doorBodyShape: doorResult.shapeId,
        toolGeometries: [toolResult.shapeId],
        operation: 'subtract'
      })
      addLog('success', `✅ Sweep operation completed: ${sweepResult.toolsProcessed} tools processed`)
      finalShapeId = sweepResult.shapeId

    } else if (testId === 'multi-tool-sweep') {
      // Create door body + multiple positioned tool operations
      // FIXED: Use consistent millimeter units throughout
      const doorWidth = 200  // mm
      const doorHeight = 150 // mm
      const doorThickness = 20 // mm

      const doorResult = await sendMessage('createDoorBody', {
        width: doorWidth / 1000,  // Convert mm to meters for OCJS
        height: doorHeight / 1000, // Convert mm to meters for OCJS
        thickness: doorThickness / 1000 // Convert mm to meters for OCJS
      })
      addLog('success', `✅ Door body created: ${doorResult.shapeId} (${doorWidth}×${doorHeight}×${doorThickness}mm)`)

      // Create positioned tool shapes for different locations
      processingStep.value = 'Creating positioned tool shapes...'

      // Tool 1: 8MM drilling operation - left side through-hole
      const tool1Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '8MM_DRILL', type: 'cylindrical', diameter: 8, shape: 'cylindrical' },
        commands: [{
          command_type: 'circle',
          x1: 60,
          y1: 40,
          radius: 0, // No radius = drilling operation
          layer_name: '8MM_DRILL' // Layer name determines operation
        }],
        depth: 10 / 1000, // 15mm converted to meters
        isBottomFace: true, // Cut from bottom surface
        doorWidth: doorWidth / 1000, // Convert mm to meters
        doorHeight: doorHeight / 1000 // Convert mm to meters
      })
      addLog('success', `✅ 8MM drilling tool created at (60,40)mm`)

      // Tool 2: 6MM groove operation - right side circular groove
      const tool2Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '6MM_GROOVE', type: 'cylindrical', diameter: 6, shape: 'cylindrical' },
        commands: [{
          command_type: 'circle',
          x1: 140,
          y1: 40,
          radius: 10, // 15mm radius for groove operation
          layer_name: 'K_6MM_GROOVE' // K_ prefix indicates groove operation
        }],
        depth: 3 / 1000, // 3mm depth for groove
        isBottomFace: true, // Cut from top surface (same as drill)
        doorWidth: doorWidth / 1000, // Convert mm to meters
        doorHeight: doorHeight / 1000 // Convert mm to meters
      })
      addLog('success', `✅ 6MM groove tool created at (140,40)mm with 15mm radius`)

      // Tool 3: 4MM pocket operation - center circular pocket
      const tool3Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '4MM_POCKET', type: 'cylindrical', diameter: 4, shape: 'cylindrical' },
        commands: [{
          command_type: 'circle',
          x1: 100,
          y1: 100,
          radius: 20, // 20mm radius for pocket operation
          layer_name: 'CEP_4MM_POCKET' // CEP prefix indicates pocket operation
        }],
        depth: 5 / 1000, // 5mm depth for pocket
        isBottomFace: true, // Cut from top surface (same as drill and groove)
        doorWidth: doorWidth / 1000, // Convert mm to meters
        doorHeight: doorHeight / 1000 // Convert mm to meters
      })
      addLog('success', `✅ 4MM pocket tool created at (100,100)mm with 20mm radius`)

      // Collect all positioned tool shape IDs
      const allToolShapeIds = [
        ...tool1Result.shapeIds,
        ...tool2Result.shapeIds,
        ...tool3Result.shapeIds
      ]

      // Perform sweep operation with positioned tools
      processingStep.value = 'Performing multi-tool sweep operation...'
      const sweepResult = await sendMessage('performSweepOperation', {
        doorBodyShape: doorResult.shapeId,
        toolGeometries: allToolShapeIds,
        operation: 'subtract'
      })
      addLog('success', `✅ Multi-operation sweep completed: ${sweepResult.toolsProcessed} tools processed (drill + groove + pocket)`)
      addLog('info', '📋 This test demonstrates: drilling (no radius), grooving (circle borderline), and pocketing (full circle area)')
      finalShapeId = sweepResult.shapeId

    } else if (testId === 'rectangle-operations') {
      // Rectangle operations test with multiple rectangular cuts
      const doorWidth = 250  // mm
      const doorHeight = 200 // mm
      const doorThickness = 20 // mm

      const doorResult = await sendMessage('createDoorBody', {
        width: doorWidth / 1000,
        height: doorHeight / 1000,
        thickness: doorThickness / 1000
      })
      addLog('success', `✅ Door body created: ${doorResult.shapeId} (${doorWidth}×${doorHeight}×${doorThickness}mm)`)

      // Create positioned tool shapes for rectangular operations
      processingStep.value = 'Creating rectangular tool operations...'

      // Rectangle 1: Pocket operation with 12MM tool
      const rect1Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '12MM_RECT_POCKET', type: 'cylindrical', diameter: 12, shape: 'cylindrical' },
        commands: [{
          command_type: 'rectangle',
          x1: 50, y1: 50, x2: 200, y2: 100,
          layer_name: 'CEP_12MM_POCKET' // CEP prefix indicates pocket operation
        }],
        depth: 8 / 1000,
        isBottomFace: true,
        doorWidth: doorWidth / 1000,
        doorHeight: doorHeight / 1000
      })

      // Rectangle 2: Groove operation with 8MM tool
      const rect2Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '8MM_RECT_GROOVE', type: 'cylindrical', diameter: 8, shape: 'cylindrical' },
        commands: [{
          command_type: 'rectangle',
          x1: 30, y1: 120, x2: 120, y2: 170,
          layer_name: 'K_8MM_GROOVE' // K_ prefix indicates groove operation
        }],
        depth: 5 / 1000,
        isBottomFace: true,
        doorWidth: doorWidth / 1000,
        doorHeight: doorHeight / 1000
      })

      // Rectangle 3: Drilling operation with 6MM tool
      const rect3Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '6MM_RECT_DRILL', type: 'cylindrical', diameter: 6, shape: 'cylindrical' },
        commands: [{
          command_type: 'rectangle',
          x1: 150, y1: 120, x2: 220, y2: 170,
          layer_name: 'DRILL_6MM' // DRILL prefix indicates drilling operation
        }],
        depth: 3 / 1000,
        isBottomFace: true,
        doorWidth: doorWidth / 1000,
        doorHeight: doorHeight / 1000
      })

      const allRectToolIds = [
        ...rect1Result.shapeIds,
        ...rect2Result.shapeIds,
        ...rect3Result.shapeIds
      ]

      // Perform sweep operation
      processingStep.value = 'Performing rectangle sweep operations...'
      const sweepResult = await sendMessage('performSweepOperation', {
        doorBodyShape: doorResult.shapeId,
        toolGeometries: allRectToolIds,
        operation: 'subtract'
      })
      addLog('success', `✅ Rectangle operations completed: ${sweepResult.toolsProcessed} rectangular operations processed`)
      addLog('info', '📋 This test demonstrates different rectangular operations based on layer names:')
      addLog('info', '📋 Rectangle 1: 150×50mm POCKET (CEP_12MM_POCKET) - full rectangular removal')
      addLog('info', '📋 Rectangle 2: 90×50mm GROOVE (K_8MM_GROOVE) - rectangular outline/border')
      addLog('info', '📋 Rectangle 3: 70×50mm DRILLING (DRILL_6MM) - cylindrical hole at rectangle center')
      finalShapeId = sweepResult.shapeId

    } else if (testId === 'line-operations') {
      // Line operations test with linear cuts
      const doorWidth = 200  // mm
      const doorHeight = 150 // mm
      const doorThickness = 18 // mm

      const doorResult = await sendMessage('createDoorBody', {
        width: doorWidth / 1000,
        height: doorHeight / 1000,
        thickness: doorThickness / 1000
      })
      addLog('success', `✅ Door body created: ${doorResult.shapeId} (${doorWidth}×${doorHeight}×${doorThickness}mm)`)

      processingStep.value = 'Creating linear tool operations...'

      // Line 1: Pocket operation with 10MM tool
      const line1Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '10MM_LINE_POCKET', type: 'cylindrical', diameter: 10, shape: 'cylindrical' },
        commands: [{
          command_type: 'line',
          x1: 20, y1: 40, x2: 180, y2: 40,
          layer_name: 'CEP_10MM_POCKET' // CEP prefix = pocket operation
        }],
        depth: 6 / 1000,
        isBottomFace: true,
        doorWidth: doorWidth / 1000,
        doorHeight: doorHeight / 1000
      })

      // Line 2: Groove operation with 8MM tool
      const line2Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '8MM_LINE_GROOVE', type: 'cylindrical', diameter: 8, shape: 'cylindrical' },
        commands: [{
          command_type: 'line',
          x1: 100, y1: 20, x2: 100, y2: 130,
          layer_name: 'K_8MM_GROOVE' // K_ prefix = groove operation
        }],
        depth: 4 / 1000,
        isBottomFace: true,
        doorWidth: doorWidth / 1000,
        doorHeight: doorHeight / 1000
      })

      // Line 3: Drilling operation with 6MM tool
      const line3Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '6MM_LINE_DRILL', type: 'cylindrical', diameter: 6, shape: 'cylindrical' },
        commands: [{
          command_type: 'line',
          x1: 30, y1: 80, x2: 170, y2: 120,
          layer_name: 'DRILL_6MM' // DRILL prefix = drilling operation
        }],
        depth: 3 / 1000,
        isBottomFace: true,
        doorWidth: doorWidth / 1000,
        doorHeight: doorHeight / 1000
      })

      const allLineToolIds = [
        ...line1Result.shapeIds,
        ...line2Result.shapeIds,
        ...line3Result.shapeIds
      ]

      // Perform sweep operation
      processingStep.value = 'Performing line sweep operations...'
      const sweepResult = await sendMessage('performSweepOperation', {
        doorBodyShape: doorResult.shapeId,
        toolGeometries: allLineToolIds,
        operation: 'subtract'
      })
      addLog('success', `✅ Line operations completed: ${sweepResult.toolsProcessed} linear operations processed`)
      addLog('info', '📋 This test demonstrates different linear operations based on layer names:')
      addLog('info', '📋 Line 1: Horizontal POCKET (CEP_10MM_POCKET) - elongated rectangular removal')
      addLog('info', '📋 Line 2: Vertical GROOVE (K_8MM_GROOVE) - elongated groove along line')
      addLog('info', '📋 Line 3: Diagonal DRILLING (DRILL_6MM) - cylindrical hole at line center')
      finalShapeId = sweepResult.shapeId

    } else if (testId === 'polyline-operations') {
      // Polyline operations test with complex paths
      const doorWidth = 220  // mm
      const doorHeight = 180 // mm
      const doorThickness = 20 // mm

      const doorResult = await sendMessage('createDoorBody', {
        width: doorWidth / 1000,
        height: doorHeight / 1000,
        thickness: doorThickness / 1000
      })
      addLog('success', `✅ Door body created: ${doorResult.shapeId} (${doorWidth}×${doorHeight}×${doorThickness}mm)`)

      processingStep.value = 'Creating polyline tool operations...'

      // Polyline 1: Pocket operation with 8MM tool
      const poly1Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '8MM_POLY_POCKET', type: 'cylindrical', diameter: 8, shape: 'cylindrical' },
        commands: [{
          command_type: 'polyline',
          points: [
            { x: 20, y: 20 }, { x: 200, y: 20 }, { x: 200, y: 160 }, { x: 20, y: 160 }, { x: 20, y: 20 }
          ],
          layer_name: 'CEP_8MM_POCKET' // CEP prefix = pocket operation
        }],
        depth: 4 / 1000,
        isBottomFace: true,
        doorWidth: doorWidth / 1000,
        doorHeight: doorHeight / 1000
      })

      // Polyline 2: Groove operation with 6MM tool - Spiral pattern to clearly show path following
      const poly2Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '6MM_POLY_GROOVE', type: 'cylindrical', diameter: 6, shape: 'cylindrical' },
        commands: [{
          command_type: 'polyline',
          points: [
            { x: 110, y: 90 },   // Start at center
            { x: 130, y: 90 },   // Move right
            { x: 130, y: 110 },  // Move down
            { x: 90, y: 110 },   // Move left (wider)
            { x: 90, y: 70 },    // Move up (wider)
            { x: 150, y: 70 },   // Move right (even wider)
            { x: 150, y: 130 },  // Move down (even wider)
            { x: 70, y: 130 },   // Move left (widest)
            { x: 70, y: 50 }     // End at top left
          ],
          layer_name: 'K_6MM_GROOVE' // K_ prefix = groove operation
        }],
        depth: 3 / 1000,
        isBottomFace: true,
        doorWidth: doorWidth / 1000,
        doorHeight: doorHeight / 1000
      })

      // Polyline 3: Drilling operation with 4MM tool
      const poly3Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '4MM_POLY_DRILL', type: 'cylindrical', diameter: 4, shape: 'cylindrical' },
        commands: [{
          command_type: 'polyline',
          points: [
            { x: 110, y: 120 }, { x: 130, y: 100 }, { x: 150, y: 120 }, { x: 130, y: 140 }, { x: 110, y: 120 }
          ],
          layer_name: 'DRILL_4MM' // DRILL prefix = drilling operation
        }],
        depth: 2 / 1000,
        isBottomFace: true,
        doorWidth: doorWidth / 1000,
        doorHeight: doorHeight / 1000
      })

      const allPolyToolIds = [
      //  ...poly1Result.shapeIds,
        ...poly2Result.shapeIds,
      //  ...poly3Result.shapeIds
      ]

      // Perform sweep operation
      processingStep.value = 'Performing polyline sweep operations...'
      const sweepResult = await sendMessage('performSweepOperation', {
        doorBodyShape: doorResult.shapeId,
        toolGeometries: allPolyToolIds,
        operation: 'subtract'
      })
      addLog('success', `✅ Polyline operations completed: ${sweepResult.toolsProcessed} polyline operations processed`)
      addLog('info', '📋 This test demonstrates different polyline operations based on layer names:')
      addLog('info', '📋 Polyline 1: Border POCKET (CEP_8MM_POCKET) - rectangular pocket covering path area')
      addLog('info', '📋 Polyline 2: Spiral GROOVE (K_6MM_GROOVE) - groove following spiral path (NOT a rectangle!)')
      addLog('info', '📋 Polyline 3: Star DRILLING (DRILL_4MM) - cylindrical hole at path center')
      finalShapeId = sweepResult.shapeId

    } else if (testId === 'polyline-performance') {
      // Polyline performance test - wire-based sweep optimization
      const doorWidth = 300  // mm
      const doorHeight = 250 // mm
      const doorThickness = 20 // mm

      const doorResult = await sendMessage('createDoorBody', {
        width: doorWidth / 1000,
        height: doorHeight / 1000,
        thickness: doorThickness / 1000
      })
      addLog('success', `✅ Door body created: ${doorResult.shapeId} (${doorWidth}×${doorHeight}×${doorThickness}mm)`)

      processingStep.value = 'Creating complex spiral polyline for performance testing...'
      const performanceStartTime = performance.now()

      // Complex spiral groove with many segments to test performance
      const spiralResult = await sendMessage('createPositionedToolShapes', {
        tool: { name: '6MM_SPIRAL_GROOVE', type: 'cylindrical', diameter: 6, shape: 'cylindrical' },
        commands: [{
          command_type: 'polyline',
          points: [
            { x: 50, y: 50 },   { x: 70, y: 50 },   { x: 70, y: 70 },   { x: 50, y: 70 },   { x: 50, y: 90 },   { x: 90, y: 90 },
            { x: 90, y: 50 },   { x: 110, y: 50 },  { x: 110, y: 110 }, { x: 50, y: 110 },  { x: 50, y: 130 },  { x: 130, y: 130 },
            { x: 130, y: 50 },  { x: 150, y: 50 },  { x: 150, y: 150 }, { x: 50, y: 150 },  { x: 50, y: 170 },  { x: 170, y: 170 },
            { x: 170, y: 50 },  { x: 190, y: 50 },  { x: 190, y: 190 }, { x: 50, y: 190 },  { x: 50, y: 210 },  { x: 210, y: 210 },
            { x: 210, y: 50 },  { x: 230, y: 50 },  { x: 230, y: 230 }, { x: 50, y: 230 }
          ],
          layer_name: 'K_6MM_GROOVE' // K_ prefix = groove operation
        }],
        depth: 3 / 1000,
        isBottomFace: true,
        doorWidth: doorWidth / 1000,
        doorHeight: doorHeight / 1000
      })

      // Perform sweep operation
      processingStep.value = 'Performing wire-based sweep operation...'
      const sweepResult = await sendMessage('performSweepOperation', {
        doorBodyShape: doorResult.shapeId,
        toolGeometries: spiralResult.shapeIds,
        operation: 'subtract'
      })

      const performanceEndTime = performance.now()
      const totalTime = performanceEndTime - performanceStartTime

      addLog('success', `✅ Performance test completed: ${sweepResult.toolsProcessed} operations processed`)
      addLog('info', `⚡ Total test time: ${totalTime.toFixed(2)}ms`)
      addLog('info', '📋 This test demonstrates wire-based sweep optimization:')
      addLog('info', '📋 - Complex spiral with 27 segments')
      addLog('info', '📋 - Wire-based approach creates continuous groove')
      addLog('info', '📋 - Check console for detailed timing information')
      addLog('info', '📋 - Significantly faster than overlapping cylinders method')
      finalShapeId = sweepResult.shapeId

    } else if (testId === 'arc-operations') {
      // Arc operations test with curved cuts
      const doorWidth = 200  // mm
      const doorHeight = 150 // mm
      const doorThickness = 18 // mm

      const doorResult = await sendMessage('createDoorBody', {
        width: doorWidth / 1000,
        height: doorHeight / 1000,
        thickness: doorThickness / 1000
      })
      addLog('success', `✅ Door body created: ${doorResult.shapeId} (${doorWidth}×${doorHeight}×${doorThickness}mm)`)

      processingStep.value = 'Creating arc tool operations...'

      // Arc 1: Pocket operation with 10MM tool
      const arc1Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '10MM_ARC_POCKET', type: 'cylindrical', diameter: 10, shape: 'cylindrical' },
        commands: [{
          command_type: 'arc',
          x1: 100, y1: 75, // Center point
          radius: 40,
          start_angle: 0,
          end_angle: 180,
          layer_name: 'CEP_10MM_POCKET' // CEP prefix = pocket operation
        }],
        depth: 5 / 1000,
        isBottomFace: true,
        doorWidth: doorWidth / 1000,
        doorHeight: doorHeight / 1000
      })

      // Arc 2: Groove operation with 8MM tool
      const arc2Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '8MM_ARC_GROOVE', type: 'cylindrical', diameter: 8, shape: 'cylindrical' },
        commands: [{
          command_type: 'arc',
          x1: 50, y1: 50, // Center point
          radius: 25,
          start_angle: 0,
          end_angle: 90,
          layer_name: 'K_8MM_GROOVE' // K_ prefix = groove operation
        }],
        depth: 4 / 1000,
        isBottomFace: true,
        doorWidth: doorWidth / 1000,
        doorHeight: doorHeight / 1000
      })

      // Arc 3: Drilling operation with 6MM tool
      const arc3Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '6MM_ARC_DRILL', type: 'cylindrical', diameter: 6, shape: 'cylindrical' },
        commands: [{
          command_type: 'arc',
          x1: 150, y1: 100, // Center point
          radius: 20,
          start_angle: 45,
          end_angle: 315,
          layer_name: 'DRILL_6MM' // DRILL prefix = drilling operation
        }],
        depth: 3 / 1000,
        isBottomFace: true,
        doorWidth: doorWidth / 1000,
        doorHeight: doorHeight / 1000
      })

      const allArcToolIds = [
        ...arc1Result.shapeIds,
        ...arc2Result.shapeIds,
        ...arc3Result.shapeIds
      ]

      // Perform sweep operation
      processingStep.value = 'Performing arc sweep operations...'
      const sweepResult = await sendMessage('performSweepOperation', {
        doorBodyShape: doorResult.shapeId,
        toolGeometries: allArcToolIds,
        operation: 'subtract'
      })
      addLog('success', `✅ Arc operations completed: ${sweepResult.toolsProcessed} arc operations processed`)
      addLog('info', '📋 This test demonstrates different arc operations based on layer names:')
      addLog('info', '📋 Arc 1: Semicircle POCKET (CEP_10MM_POCKET) - circular pocket at arc center')
      addLog('info', '📋 Arc 2: Quarter GROOVE (K_8MM_GROOVE) - annular groove around arc radius')
      addLog('info', '📋 Arc 3: Three-quarter DRILLING (DRILL_6MM) - cylindrical hole at arc center')
      finalShapeId = sweepResult.shapeId

    } else if (testId === 'polyline-sweep') {
      // Polyline sweep test using the new createSweepFromPolyline function
      const doorWidth = 200  // mm
      const doorHeight = 150 // mm
      const doorThickness = 18 // mm

      const doorResult = await sendMessage('createDoorBody', {
        width: doorWidth / 1000,
        height: doorHeight / 1000,
        thickness: doorThickness / 1000
      })
      addLog('success', `✅ Door body created: ${doorResult.shapeId} (${doorWidth}×${doorHeight}×${doorThickness}mm)`)

      processingStep.value = 'Creating profile shape for sweep...'
      
      // Create a circular profile for the sweep operation
      const profileResult = await sendMessage('createToolBRep', {
        tool: { name: '6MM_PROFILE', shape: 'cylindrical', diameter: 6 },
        height: 25 // Ensure sufficient height for extrusion (25mm)
      })
      addLog('success', `✅ Profile shape created: ${profileResult.shapeId}`)

      processingStep.value = 'Testing createSweepFromPolyline function...'
      
      // Test the new createSweepFromPolyline function with a custom polyline
      const polylinePoints = [
        { x: -0.05, y: 0, z: 0 },      // Start point (50mm left from center, at surface)
        { x: 0.05, y: 0, z: 0 },       // Move right 100mm
        { x: 0.05, y: -0.05, z: 0 },   // Move down 50mm
        { x: -0.05, y: -0.05, z: 0 },  // Move left 100mm
        { x: -0.05, y: 0, z: 0 }       // Back to start (closed path)
      ]
      
      const sweepResult = await sendMessage('testPolylineSweep', {
        polylinePoints: polylinePoints,
        profileShape: profileResult.shapeId,
        doorBodyShape: doorResult.shapeId
      })
      
      addLog('success', `✅ Polyline sweep completed successfully`)
      addLog('info', '📋 This test demonstrates the new createSweepFromPolyline function:')
      addLog('info', '📋 - Creates a rectangular path with 5 points')
      addLog('info', '📋 - Sweeps a 6mm circular profile along the path')
      addLog('info', '📋 - Uses wire-based sweep for smooth continuous operation')
      addLog('info', '📋 - Polyline points are converted to OpenCascade gp_Pnt objects')
      addLog('info', '📋 - Edges are created between consecutive points')
      addLog('info', '📋 - Wire is built from edges and validated')
      addLog('info', '📋 - BRepOffsetAPI_MakePipe performs the sweep operation')
      
      finalShapeId = sweepResult.shapeId

    } else {
      // For other tests, create door body as default
      const doorResult = await sendMessage('createDoorBody', {
        width: 200,
        height: 150,
        thickness: 20
      })
      addLog('success', `✅ Door body created: ${doorResult.shapeId}`)
      finalShapeId = doorResult.shapeId
    }

    // Export the result to GLB for visualization
    processingStep.value = 'Exporting to GLB for visualization...'
    addLog('info', '📦 Exporting final 3D model to GLB format...')
    const glbData = await sendMessage('exportGLB', finalShapeId)
    addLog('info', `📊 GLB file size: ${(glbData.byteLength / 1024).toFixed(1)} KB`)

    // Load the 3D model
    processingStep.value = 'Loading 3D visualization...'
    await loadModel(glbData)
    
    const result = { success: true, message: 'Test completed successfully', timestamp: new Date().toLocaleTimeString() }
    testResults.value.set(testId, result)
    lastTestResult.value = result
    
    addLog('success', `✅ Test completed: ${testDef.name}`)
    
  } catch (error) {
    const result = { success: false, message: `Test failed: ${error}`, timestamp: new Date().toLocaleTimeString() }
    testResults.value.set(testId, result)
    lastTestResult.value = result
    
    addLog('error', `❌ Test failed: ${error}`)
  } finally {
    isRunning.value = false
    currentTest.value = ''
    processingStep.value = ''
  }
}

async function loadModel(glbData: ArrayBuffer) {
  if (!scene) return

  addLog('info', `Loading 3D model (${glbData.byteLength} bytes)...`)

  // Clean up previous model
  if (model) {
    scene.remove(model)
  }

  // Create blob URL
  const blob = new Blob([glbData], { type: 'model/gltf-binary' })
  const url = URL.createObjectURL(blob)

  if (modelUrl.value) {
    URL.revokeObjectURL(modelUrl.value)
  }
  modelUrl.value = url

  // Load with GLTFLoader
  const loader = new GLTFLoader()
  return new Promise((resolve, reject) => {
    loader.load(
      url,
      (gltf) => {
        model = gltf.scene
        scene!.add(model)

        // Get model statistics
        let meshCount = 0
        let vertexCount = 0
        model.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            meshCount++
            if (child.geometry) {
              const positions = child.geometry.attributes.position
              if (positions) {
                vertexCount += positions.count
              }
            }
          }
        })

        // Center the model
        const box = new THREE.Box3().setFromObject(model)
        const center = box.getCenter(new THREE.Vector3())
        model.position.sub(center)

        // Scale the model to fit the view
        const size = box.getSize(new THREE.Vector3())
        const maxDim = Math.max(size.x, size.y, size.z)
        if (maxDim > 0) {
          const scale = 200 / maxDim // Scale to fit in 200 units (larger scale)
          model.scale.setScalar(scale)
          addLog('info', `🔍 Model scaled by factor: ${scale.toFixed(3)}`)
        }

        // Add a bright material to make it more visible
        model.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            child.material = new THREE.MeshLambertMaterial({
              color: 0x8B4513, // Brown color for wood
              wireframe: wireframeMode.value
            })
          }
        })

        addLog('success', `✅ 3D model loaded successfully`)
        addLog('info', `📊 Model stats: ${meshCount} meshes, ${vertexCount} vertices`)
        addLog('info', `📏 Model size: ${size.x.toFixed(1)} x ${size.y.toFixed(1)} x ${size.z.toFixed(1)} units`)
        resolve(gltf)
      },
      (progress) => {
        // Loading progress
        const percent = (progress.loaded / progress.total) * 100
        addLog('info', `Loading progress: ${percent.toFixed(1)}%`)
      },
      (error) => {
        addLog('error', `❌ Failed to load 3D model: ${error}`)
        reject(error)
      }
    )
  })
}

function clearResults() {
  consoleOutput.value = []
  testResults.value.clear()
  lastTestResult.value = null
  
  if (model && scene) {
    scene.remove(model)
    model = null
  }
  
  if (modelUrl.value) {
    URL.revokeObjectURL(modelUrl.value)
    modelUrl.value = null
  }
}

async function runAllTests() {
  const testIds = Object.keys(testDefinitions)
  
  for (const testId of testIds) {
    if (!isRunning.value) break
    await runTest(testId)
    await new Promise(resolve => setTimeout(resolve, 1000)) // Pause between tests
  }
}

function toggleWireframe() {
  if (!model) return

  model.traverse((child) => {
    if (child instanceof THREE.Mesh) {
      if (child.material) {
        if (Array.isArray(child.material)) {
          child.material.forEach(mat => {
            mat.wireframe = wireframeMode.value
          })
        } else {
          child.material.wireframe = wireframeMode.value
        }
      }
    }
  })

  addLog('info', `🔧 Wireframe mode: ${wireframeMode.value ? 'ON' : 'OFF'}`)
}

function resetCamera() {
  if (!camera || !controls) return

  // Reset camera position
  camera.position.set(150, 150, 150)
  camera.lookAt(0, 0, 0)

  // Reset controls
  controls.reset()

  addLog('info', '📷 Camera view reset')
}

function toggleDebugMarkers() {
  if (!scene) return

  if (debugMode.value) {
    // Add debug markers at tool positions
    addDebugMarkers()
  } else {
    // Remove debug markers
    clearDebugMarkers()
  }

  addLog('info', `🔍 Debug markers: ${debugMode.value ? 'ON' : 'OFF'}`)
}

function addDebugMarkers() {
  if (!scene) return

  // Clear existing markers
  clearDebugMarkers()

  // Tool positions from the console logs (converted to Three.js coordinates)
  const toolPositions = [
    { x: -0.04, y: 0.0125, z: -0.035, color: 0xff0000, size: 20, name: '8MM' }, // Red for 8MM - LARGE
    { x: 0.04, y: 0.0125, z: -0.035, color: 0x00ff00, size: 20, name: '6MM' },  // Green for 6MM - LARGE
    { x: 0.0, y: 0.0125, z: 0.025, color: 0x0000ff, size: 20, name: '4MM' },    // Blue for 4MM - LARGE
    // Add some reference markers that should definitely be visible
    { x: 0, y: 50, z: 0, color: 0xffff00, size: 30, name: 'ABOVE' },           // Yellow above door
    { x: 50, y: 0, z: 0, color: 0xff00ff, size: 30, name: 'RIGHT' },           // Magenta to the right
    { x: 0, y: 0, z: 50, color: 0x00ffff, size: 30, name: 'FRONT' }            // Cyan in front
  ]

  toolPositions.forEach((pos, index) => {
    // Create sphere geometry for the marker - MUCH LARGER
    const geometry = new THREE.SphereGeometry(pos.size, 16, 16) // Use size directly (no conversion)
    const material = new THREE.MeshBasicMaterial({
      color: pos.color,
      transparent: false, // Make them solid
      opacity: 1.0
    })
    const sphere = new THREE.Mesh(geometry, material)

    // Position the marker
    sphere.position.set(pos.x, pos.y, pos.z)

    // Add to scene - check if scene exists
    if (scene) {
      scene.add(sphere)
      debugMarkers.push(sphere)
    }

    const colorName = pos.color === 0xff0000 ? 'RED' :
                      pos.color === 0x00ff00 ? 'GREEN' :
                      pos.color === 0x0000ff ? 'BLUE' :
                      pos.color === 0xffff00 ? 'YELLOW' :
                      pos.color === 0xff00ff ? 'MAGENTA' :
                      pos.color === 0x00ffff ? 'CYAN' : 'UNKNOWN'
    addLog('info', `🎯 Debug marker ${index + 1}: ${colorName} ${pos.name} at (${pos.x}, ${pos.y}, ${pos.z})`)
  })

  addLog('success', `✅ Added ${debugMarkers.length} debug markers`)
}

function clearDebugMarkers() {
  if (!scene) return

  debugMarkers.forEach(marker => {
    scene!.remove(marker)
  })
  debugMarkers = []
}

function cleanup() {
  if (worker) {
    worker.terminate()
  }

  if (renderer && threeContainer.value) {
    threeContainer.value.removeChild(renderer.domElement)
    renderer.dispose()
  }

  if (modelUrl.value) {
    URL.revokeObjectURL(modelUrl.value)
  }
}
</script>

<style scoped>
.test-button {
  transition: all 0.2s ease;
}

.test-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.test-category {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

.test-category:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
</style>
